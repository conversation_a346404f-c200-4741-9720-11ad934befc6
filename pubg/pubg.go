package pubg

import (
	"fmt"
	"log"
	"math"
	"pubghelper/config"
	"pubghelper/models"
	"pubghelper/vmm"
	"strings"
	"time"
)

// PUBG represents the main PUBG memory reader
type PUBG struct {
	vmm           *vmm.VMM
	pid           uint32
	moduleBase    uint64
	gNamesAddress uint64
	offset        *models.Offset
	goodItems     []models.GoodItem
	carList       []models.CarModel
	isLocal       bool
	isKaiWuZi     bool
	myModel       *models.PlayerModel
	
	// Events
	OnPlayerListUpdate func(*models.PubgModel)
	OnExecTime         func(int64, string)
}

// DecryptFunc represents the decryption function type
type DecryptFunc func(uint64) uint64

// NewPUBG creates a new PUBG instance
func NewPUBG() *PUBG {
	return &PUBG{
		carList: models.GetCarList(),
	}
}

// Init initializes the PUBG memory reader
func (p *PUBG) Init(isLocal bool) (bool, string) {
	p.isLocal = isLocal
	
	// Load configuration
	var err error
	p.offset, err = config.LoadOffset("offset.txt")
	if err != nil {
		log.Printf("Failed to load offset config: %v", err)
		p.offset = models.GetDefaultOffset()
	}
	
	p.goodItems, err = config.LoadGoodItems("itemfilter.json")
	if err != nil {
		log.Printf("Failed to load good items config: %v", err)
	}
	
	// Initialize VMM
	p.vmm, err = vmm.Initialize("", isLocal)
	if err != nil {
		return false, fmt.Sprintf("VMM initialization failed: %v", err)
	}
	
	// Find game process
	pids, err := p.vmm.PidList()
	if err != nil {
		return false, fmt.Sprintf("Failed to get process list: %v", err)
	}
	
	for _, pid := range pids {
		path := p.vmm.ProcessGetInformationString(pid, 0x10000000) // VMMDLL_PROCESS_INFORMATION_OPT_STRING_PATH_KERNEL
		if strings.Contains(strings.ToLower(path), "tslgame.exe") {
			moduleBase := p.vmm.ProcessGetModuleBase(pid, "TslGame.exe")
			if moduleBase > 0 {
				decryptAddr := p.vmm.MemReadInt64(pid, moduleBase+p.offset.OffsetXenuineDecrypt)
				if decryptAddr > 0 {
					p.pid = pid
					p.moduleBase = moduleBase
					break
				}
			}
		}
	}
	
	if p.pid == 0 {
		return false, "Game process not found"
	}
	
	if p.moduleBase == 0 {
		return false, "Module base not found"
	}
	
	// Setup decryption function (simplified - in real implementation you'd need to handle the actual decryption)
	// This is a placeholder since the actual decryption requires complex assembly code execution
	
	return true, ""
}

// Start starts the memory reading loop
func (p *PUBG) Start() {
	go p.readLoop()
}

// SetKaiWuZi sets whether to read items
func (p *PUBG) SetKaiWuZi(enabled bool) {
	p.isKaiWuZi = enabled
}

// Close closes the PUBG instance
func (p *PUBG) Close() {
	if p.vmm != nil {
		p.vmm.Close()
	}
}

// readLoop is the main memory reading loop
func (p *PUBG) readLoop() {
	for {
		startTime := time.Now()
		
		if p.isLocal {
			time.Sleep(30 * time.Millisecond)
		}
		
		model, err := p.readGameData()
		if err != nil {
			if p.OnExecTime != nil {
				p.OnExecTime(0, err.Error())
			}
			continue
		}
		
		if model != nil && len(model.Players) > 0 {
			if p.OnPlayerListUpdate != nil {
				p.OnPlayerListUpdate(model)
			}
		}
		
		elapsed := time.Since(startTime).Milliseconds()
		if p.OnExecTime != nil {
			p.OnExecTime(elapsed, "")
		}
	}
}

// readGameData reads all game data from memory
func (p *PUBG) readGameData() (*models.PubgModel, error) {
	scatter := p.vmm.ScatterInitialize(p.pid, vmm.FlagNoCache)
	if scatter == nil {
		return nil, fmt.Errorf("failed to initialize scatter")
	}
	defer scatter.Close()
	
	// Read basic game structures
	world := p.decrypt(uint64(p.vmm.MemReadInt64(p.pid, p.moduleBase+p.offset.OffsetGWorld)))
	gameState := p.decrypt(uint64(p.vmm.MemReadInt64(p.pid, world+p.offset.OffsetGameState)))

	tempULocalPlayer := uint64(p.vmm.MemReadInt64(p.pid, gameState+p.offset.OffsetLocalPlayers))
	uLocalPlayer := p.decrypt(uint64(p.vmm.MemReadInt64(p.pid, tempULocalPlayer)))
	playerController := p.decrypt(uint64(p.vmm.MemReadInt64(p.pid, uLocalPlayer+p.offset.OffsetPlayerController)))
	cameraManager := uint64(p.vmm.MemReadInt64(p.pid, playerController+p.offset.OffsetPlayerCameraManager))
	_ = p.vmm.MemReadVector(p.pid, cameraManager+p.offset.OffsetCameraLocation) // cameraLocation for future use

	persistentLevel := p.decrypt(uint64(p.vmm.MemReadInt64(p.pid, world+p.offset.OffsetCurrentLevel)))
	actorsArray := p.decrypt(uint64(p.vmm.MemReadInt64(p.pid, persistentLevel+p.offset.OffsetActors)))
	actorsCount := p.vmm.MemReadInt32(p.pid, actorsArray+0x08)
	actorBase := uint64(p.vmm.MemReadInt64(p.pid, actorsArray))

	gNames := p.decrypt(uint64(p.vmm.MemReadInt64(p.pid, p.moduleBase+p.offset.OffsetFNameEntry)))
	p.gNamesAddress = p.decrypt(uint64(p.vmm.MemReadInt64(p.pid, gNames)))
	
	mapID := p.decObjID(p.vmm.MemReadInt32(p.pid, world+p.offset.OffsetObjID))
	localPlayerPawn := p.decrypt(uint64(p.vmm.MemReadInt64(p.pid, playerController+p.offset.OffsetAcknowledgedPawn)))
	characterID := uint64(p.vmm.MemReadInt64(p.pid, localPlayerPawn+p.offset.OffsetCharacterName))
	myName := p.vmm.MemReadString(p.pid, characterID, 64)
	mapName := p.getObjName(mapID)
	
	if mapName == "TslLobby_Persistent_Main" {
		return nil, fmt.Errorf("in lobby")
	}
	
	if actorsCount > 20000 {
		return nil, fmt.Errorf("too many actors: %d", actorsCount)
	}
	
	model := &models.PubgModel{
		MapName: mapName,
		MyName:  myName,
	}
	
	// Read all actor pointers first
	var zhiZhenModels []models.ZhiZhenModel
	for i := int32(0); i < actorsCount; i++ {
		scatter.Prepare(uint64(actorBase)+uint64(i)*8, 8)
	}
	
	if !scatter.Execute() {
		return nil, fmt.Errorf("failed to execute scatter for actors")
	}
	
	for i := int32(0); i < actorsCount; i++ {
		pObjPointer := scatter.ReadUInt64(uint64(actorBase) + uint64(i)*8)
		if pObjPointer > 0x100000 {
			zhiZhenModels = append(zhiZhenModels, models.ZhiZhenModel{
				PObjectPointer: pObjPointer,
			})
		}
	}
	
	// Read actor IDs
	scatter.Clear(p.pid, vmm.FlagNoCache)
	for i := range zhiZhenModels {
		scatter.Prepare(zhiZhenModels[i].PObjectPointer+p.offset.OffsetObjID, 4)
	}
	
	if !scatter.Execute() {
		return nil, fmt.Errorf("failed to execute scatter for actor IDs")
	}
	
	for i := range zhiZhenModels {
		actorID := scatter.ReadInt(zhiZhenModels[i].PObjectPointer + p.offset.OffsetObjID)
		objID := p.decObjID(actorID)
		zhiZhenModels[i].ActorID = int(actorID)
		zhiZhenModels[i].ObjID = objID
	}
	
	// Continue with the rest of the data reading process...
	// This is a simplified version - the full implementation would continue
	// with reading class names, player data, vehicles, items, etc.
	
	return model, nil
}

// decrypt is a placeholder for the decryption function
// In the real implementation, this would call the actual decryption assembly code
func (p *PUBG) decrypt(value uint64) uint64 {
	// This is a placeholder - actual implementation would use the decryption function
	// created from the assembly code read from the game process
	return value
}

// decObjID decrypts object ID
func (p *PUBG) decObjID(value int32) uint32 {
	v18 := p.ror4(uint32(value^int32(p.offset.OffsetXorKey1)), p.offset.OffsetRorValue, p.offset.OffsetIsUsingRor)
	return v18 ^ (v18 << 16) ^ p.offset.OffsetXorKey2
}

// ror4 performs 32-bit rotation
func (p *PUBG) ror4(x uint32, count int, isRor bool) uint32 {
	count %= 32
	if isRor {
		return (x << (32 - count)) | (x >> count)
	}
	return (x << count) | (x >> (32 - count))
}

// getObjName gets object name by ID
func (p *PUBG) getObjName(objID uint32) string {
	fNamePtr := p.vmm.MemReadInt64(p.pid, p.gNamesAddress+uint64(objID/uint32(p.offset.OffsetChunkSize))*8)
	if fNamePtr > 0 {
		fName := p.vmm.MemReadInt64(p.pid, uint64(fNamePtr)+uint64(objID%uint32(p.offset.OffsetChunkSize))*8)
		if fName > 0 {
			nameBytes, err := p.vmm.MemRead(p.pid, uint64(fName)+0x10, 64)
			if err != nil {
				return ""
			}
			
			// Find null terminator
			for i, b := range nameBytes {
				if b == 0 {
					return string(nameBytes[:i])
				}
			}
			return string(nameBytes)
		}
	}
	return ""
}

// calculateAimFov calculates aim field of view
func (p *PUBG) calculateAimFov(actorLocation, cameraLocation models.Vector3D) models.Vector3D {
	tempV := actorLocation.Subtract(cameraLocation)
	radpi := 180.0 / math.Pi
	yaw := math.Atan2(tempV.Y, tempV.X) * radpi
	pitch := math.Atan2(actorLocation.Z, math.Sqrt(tempV.X*tempV.X+tempV.Y*tempV.Y)) * radpi
	roll := 0.0
	
	return models.Vector3D{X: yaw, Y: pitch, Z: roll}
}

// isPlayerClass checks if class name is a player class
func (p *PUBG) isPlayerClass(className string) bool {
	playerClasses := []string{
		"PlayerMale_A_C",
		"PlayerFemale_A_C",
		"AIPawn_Base_Female_C",
		"AIPawn_Base_Male_C",
		"UltAIPawn_Base_Female_C",
		"UltAIPawn_Base_Male_C",
	}
	
	for _, class := range playerClasses {
		if className == class {
			return true
		}
	}
	return false
}

// isCarClass checks if class name is a vehicle class
func (p *PUBG) isCarClass(className string) bool {
	for _, car := range p.carList {
		if car.CarClass == className {
			return true
		}
	}
	return false
}

// getCarName gets vehicle name by class
func (p *PUBG) getCarName(className string) string {
	for _, car := range p.carList {
		if car.CarClass == className {
			return car.CarName
		}
	}
	return className
}

// findGoodItem finds item configuration by class name
func (p *PUBG) findGoodItem(className string) *models.GoodItem {
	for _, item := range p.goodItems {
		if item.ClassName == className {
			return &item
		}
	}
	return nil
}
