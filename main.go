package main

import (
	"bufio"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"pubghelper/config"
	"pubghelper/models"
	"pubghelper/mqtt"
	"pubghelper/pubg"
	"strings"
	"time"
)

// Application represents the main application
type Application struct {
	pubgReader *pubg.PUBG
	mqttClient *mqtt.Client
	topic      string
	isRunning  bool
	isKaiWuZi  bool
}

// NewApplication creates a new application instance
func NewApplication() *Application {
	return &Application{
		pubgReader: pubg.NewPUBG(),
	}
}

// Run starts the application
func (app *Application) Run() {
	fmt.Println("PUBG Helper - Golang Version")
	fmt.Println("=============================")

	// Get user input
	reader := bufio.NewReader(os.Stdin)

	fmt.Print("请输入账号: ")
	account, _ := reader.ReadString('\n')
	account = strings.TrimSpace(account)

	if account == "" {
		fmt.Println("账号不能为空")
		return
	}

	app.topic = account

	fmt.Print("选择类型 (1=本地, 2=双机): ")
	typeInput, _ := reader.ReadString('\n')
	typeInput = strings.TrimSpace(typeInput)

	isLocal := typeInput == "1"

	fmt.Print("是否开启物资显示 (y/n): ")
	wuziInput, _ := reader.ReadString('\n')
	wuziInput = strings.TrimSpace(wuziInput)

	app.isKaiWuZi = strings.ToLower(wuziInput) == "y" || strings.ToLower(wuziInput) == "yes"

	fmt.Print("是否启用测试模式 (y/n): ")
	testInput, _ := reader.ReadString('\n')
	testInput = strings.TrimSpace(testInput)

	isTestMode := strings.ToLower(testInput) == "y" || strings.ToLower(testInput) == "yes"

	// Initialize PUBG reader
	fmt.Println("正在初始化DMA...")
	success, msg := app.pubgReader.Init(isLocal)
	if !success {
		if isTestMode {
			fmt.Printf("DMA初始化失败: %s\n", msg)
			fmt.Println("启用测试模式，将使用模拟数据...")
			app.pubgReader.SetTestMode(true)
			success = true
		} else {
			fmt.Printf("初始化DMA失败: %s\n", msg)
			fmt.Println("提示: 可以启用测试模式来验证程序功能")
			return
		}
	}

	if success {
		if isTestMode {
			fmt.Println("测试模式初始化成功")
		} else {
			fmt.Println("DMA初始化成功")
		}
	}

	// Setup event handlers
	app.pubgReader.OnPlayerListUpdate = app.onPlayerListUpdate
	app.pubgReader.OnExecTime = app.onExecTime
	app.pubgReader.SetKaiWuZi(app.isKaiWuZi)

	// Initialize MQTT client
	//mqttBroker := app.getMQTTBroker(account)
	//app.mqttClient = mqtt.NewClient(mqttBroker, app.topic)
	//
	//fmt.Printf("正在连接MQTT服务器: %s\n", mqttBroker)
	//if err := app.mqttClient.Connect(); err != nil {
	//	fmt.Printf("MQTT连接失败: %v\n", err)
	//	return
	//}
	//
	//fmt.Println("MQTT连接成功")

	// Start PUBG reader
	app.pubgReader.Start()
	app.isRunning = true

	// Open web interface if not special account
	if !strings.Contains(account, "*********") && !strings.Contains(account, "binbin") {
		go func() {
			time.Sleep(2 * time.Second)
			//fmt.Printf("打开网页界面: http://pubg.xzbapi.com/?%s&addr=%s\n", account, mqttBroker)
		}()
	}

	// Wait for user input to exit
	fmt.Println("程序正在运行，按回车键退出...")
	reader.ReadString('\n')

	app.Stop()
}

// Stop stops the application
func (app *Application) Stop() {
	app.isRunning = false

	if app.mqttClient != nil {
		app.mqttClient.Disconnect()
	}

	if app.pubgReader != nil {
		app.pubgReader.Close()
	}

	fmt.Println("程序已退出")
}

// onPlayerListUpdate handles player list updates
func (app *Application) onPlayerListUpdate(model *models.PubgModel) {
	if !app.mqttClient.IsConnected() {
		return
	}

	// Convert to MQTT model
	mqttModel := &models.PubgMqttModel{
		Map:    model.MapName,
		MyTeam: model.MyTeam,
		MyName: model.MyName,
		Game:   model.Game,
		Player: make([][]interface{}, 0),
		Car:    make([][]interface{}, 0),
		Goods:  make([][]interface{}, 0),
	}

	// Convert players
	for _, player := range model.Players {
		playerData := []interface{}{
			player.X,
			player.Y,
			player.Distance,
			player.TeamID,
			player.HP,
			player.KillCount,
			player.SpectatedCount,
			player.Orientation,
		}

		// Add team flag
		if player.IsMyTeam {
			playerData = append(playerData, 1)
		} else {
			playerData = append(playerData, 0)
		}

		// Add bot flag
		if player.IsBot {
			playerData = append(playerData, 1)
		} else {
			playerData = append(playerData, 0)
		}

		playerData = append(playerData, 0, 0)              // Reserved fields
		playerData = append(playerData, player.IsAimed, 0) // Aimed flag and reserved
		playerData = append(playerData, player.Name, 1)    // Name and status

		mqttModel.Player = append(mqttModel.Player, playerData)
	}

	// Convert cars
	for _, car := range model.Cars {
		carData := []interface{}{
			car.CarName,
			car.X,
			car.Y,
			car.CarClass,
		}
		mqttModel.Car = append(mqttModel.Car, carData)
	}

	// Convert goods
	for _, good := range model.PubgGoods {
		goodData := []interface{}{
			good.Name,
			good.X,
			good.Y,
			models.GetColorByGroup(good.Group),
			good.ClassName,
			good.Group,
		}
		mqttModel.Goods = append(mqttModel.Goods, goodData)
	}

	// Publish to MQTT
	jsonData, err := json.Marshal(mqttModel)
	if err != nil {
		log.Printf("JSON序列化失败: %v", err)
		return
	}

	app.mqttClient.PublishAsync(string(jsonData), func(err error) {
		if err != nil {
			log.Printf("MQTT发布失败: %v", err)
		}
	})
}

// onExecTime handles execution time updates
func (app *Application) onExecTime(execTime int64, message string) {
	if execTime > 0 {
		fmt.Printf("\r执行时间: %dms", execTime)
	}

	if message != "" {
		fmt.Printf("\n%s\n", message)
	}
}

// getMQTTBroker determines the MQTT broker to use
func (app *Application) getMQTTBroker(account string) string {
	// Check for special accounts
	if strings.Contains(account, "*********") || strings.Contains(account, "binbin") {
		return "121.10.141.68"
	}

	// Check for custom MQTT configuration
	broker := config.LoadMQTTBroker("mqtt.txt")
	if broker != "" {
		return broker
	}

	// Default broker
	return "219.129.239.39"
}

func main() {
	app := NewApplication()
	app.Run()
}
