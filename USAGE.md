# 使用指南

## 快速开始

### 1. 运行程序

双击 `pubghelper.exe` 或在命令行运行：

```bash
./pubghelper.exe
```

### 2. 配置选项

程序启动后会依次询问以下配置：

#### 账号设置
```
请输入账号: [输入你的账号名]
```
- 用作MQTT主题的唯一标识符
- 建议使用英文和数字组合

#### 运行模式
```
选择类型 (1=本地, 2=双机): [输入1或2]
```
- **1 = 本地模式**: 直接读取本机内存（需要管理员权限）
- **2 = 双机模式**: 通过DMA硬件读取（需要专用硬件）

#### 物资显示
```
是否开启物资显示 (y/n): [输入y或n]
```
- **y**: 显示游戏中的物资信息
- **n**: 不显示物资信息（提高性能）

#### 测试模式（新增）
```
是否启用测试模式 (y/n): [输入y或n]
```
- **y**: 启用测试模式，使用模拟数据
- **n**: 正常模式，需要PUBG游戏运行

## 运行模式说明

### 正常模式

**前提条件**:
- PUBG游戏正在运行
- 以管理员权限运行程序
- 游戏版本为35.2.2.3

**运行流程**:
1. 程序检测PUBG游戏进程
2. 读取游戏内存数据
3. 解析玩家、载具、物资信息
4. 通过MQTT发送数据

### 测试模式

**适用场景**:
- 验证程序基本功能
- 测试MQTT连接
- 开发和调试

**模拟数据**:
- 2个玩家（1个敌人，1个队友）
- 1辆载具（轿车）
- 1个物资（AK47）
- 安全区信息

## 示例运行

### 正常模式示例
```
PUBG Helper - Golang Version
=============================
请输入账号: myaccount
选择类型 (1=本地, 2=双机): 1
是否开启物资显示 (y/n): y
是否启用测试模式 (y/n): n
正在初始化DMA...
VMM: Initializing with device=, isLocal=true
DMA初始化成功
正在连接MQTT服务器: 219.129.239.39
MQTT连接成功
程序正在运行，按回车键退出...
执行时间: 25ms
```

### 测试模式示例
```
PUBG Helper - Golang Version
=============================
请输入账号: test
选择类型 (1=本地, 2=双机): 1
是否开启物资显示 (y/n): n
是否启用测试模式 (y/n): y
正在初始化DMA...
VMM: Initializing with device=, isLocal=true
DMA初始化失败: Game process not found
启用测试模式，将使用模拟数据...
Warning: Game process not found, enabling test mode
测试模式初始化成功
正在连接MQTT服务器: 219.129.239.39
MQTT连接成功
程序正在运行，按回车键退出...
执行时间: 15ms
```

## 快速测试

### 使用测试脚本
运行 `test_demo.bat` 进行自动化测试：

```bash
./test_demo.bat
```

### 手动测试
1. 运行 `pubghelper.exe`
2. 输入测试账号
3. 选择本地模式 (1)
4. 不开启物资显示 (n)
5. 启用测试模式 (y)
6. 观察程序输出

## 故障排除

### 常见问题

#### 1. "Game process not found"
**原因**: 没有运行PUBG游戏或游戏进程名不匹配
**解决**: 
- 启动PUBG游戏
- 或者启用测试模式验证功能

#### 2. "MQTT连接失败"
**原因**: 网络连接问题或服务器不可达
**解决**:
- 检查网络连接
- 确认防火墙设置
- 尝试使用自定义MQTT服务器

#### 3. "权限不足"
**原因**: 程序需要管理员权限读取内存
**解决**:
- 右键程序选择"以管理员身份运行"
- 或者使用测试模式

#### 4. "偏移量错误"
**原因**: 游戏版本与偏移量不匹配
**解决**:
- 确认游戏版本为35.2.2.3
- 更新offset.txt配置文件

### 调试技巧

#### 启用详细日志
程序会显示执行时间和状态信息：
```
执行时间: 25ms
```

#### 检查配置文件
确认以下文件存在且格式正确：
- `offset.txt` - 内存偏移量
- `itemfilter.json` - 物资过滤
- `mqtt.txt` - MQTT服务器（可选）

#### 网络测试
可以使用MQTT客户端工具验证数据传输：
- 主题: 你输入的账号名
- 服务器: 219.129.239.39:1883

## 性能优化

### 提高性能
1. **禁用物资显示**: 减少内存读取量
2. **使用本地模式**: 避免网络延迟
3. **关闭不必要的程序**: 释放系统资源

### 监控性能
- 观察执行时间（建议<50ms）
- 监控CPU使用率（建议<5%）
- 检查内存占用（建议<50MB）

## 安全建议

1. **仅在测试环境使用**: 避免在正式游戏中使用
2. **遵守游戏条款**: 不要用于获得不公平优势
3. **保护隐私**: 不要分享敏感的配置信息
4. **定期更新**: 保持偏移量配置最新

## 技术支持

如果遇到问题：
1. 查看本文档的故障排除部分
2. 检查配置文件格式
3. 尝试测试模式验证基本功能
4. 在GitHub Issues中报告问题

---

**提示**: 测试模式是验证程序功能的好方法，即使没有游戏运行也能确认程序正常工作。
