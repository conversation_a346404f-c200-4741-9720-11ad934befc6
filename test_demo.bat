@echo off
echo ========================================
echo PUBG Helper 测试模式演示
echo ========================================
echo.
echo 这个脚本将演示测试模式功能
echo 即使没有运行PUBG游戏也能验证程序功能
echo.
echo 程序将会：
echo 1. 初始化DMA（会失败，因为没有游戏）
echo 2. 自动启用测试模式
echo 3. 生成模拟的游戏数据
echo 4. 通过MQTT发送测试数据
echo.
echo 按任意键开始测试...
pause >nul

echo.
echo 正在启动测试模式...
echo.

REM 使用echo命令模拟用户输入
(
echo test_account
echo 1
echo n
echo y
) | pubghelper.exe

echo.
echo 测试完成！
echo.
echo 如果看到以上输出，说明程序基本功能正常
echo 可以在有PUBG游戏运行时使用正常模式
echo.
pause
