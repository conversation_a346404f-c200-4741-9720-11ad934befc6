package config

import (
	"encoding/json"
	"io/ioutil"
	"os"
	"pubghelper/models"
	"reflect"
	"strconv"
	"strings"
)

// Config represents the application configuration
type Config struct {
	Offset    *models.Offset         `json:"offset"`
	GoodItems []models.GoodItem      `json:"goodItems"`
	MQTTBroker string                `json:"mqttBroker"`
}

// LoadOffset loads offset configuration from file
func LoadOffset(filename string) (*models.Offset, error) {
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		return models.GetDefaultOffset(), nil
	}

	data, err := ioutil.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var offsetData map[string]interface{}
	if err := json.Unmarshal(data, &offsetData); err != nil {
		return nil, err
	}

	offset := &models.Offset{}

	// Use reflection to automatically populate all fields
	offsetValue := reflect.ValueOf(offset).Elem()
	offsetType := offsetValue.Type()

	for i := 0; i < offsetValue.NumField(); i++ {
		field := offsetValue.Field(i)
		fieldType := offsetType.Field(i)

		// Get the JSON tag name
		jsonTag := fieldType.Tag.Get("json")
		if jsonTag == "" {
			continue
		}

		// Get the value from the JSON data
		if value, exists := offsetData[jsonTag]; exists && field.CanSet() {
			switch field.Kind() {
			case reflect.Uint64:
				field.SetUint(parseUint64(value))
			case reflect.Uint32:
				field.SetUint(uint64(parseUint32(value)))
			case reflect.Int:
				field.SetInt(int64(parseInt(value)))
			case reflect.Int64:
				field.SetInt(parseInt64(value))
			case reflect.Bool:
				field.SetBool(parseBool(value))
			}
		}
	}

	return offset, nil
}

// LoadGoodItems loads item filter configuration from file
func LoadGoodItems(filename string) ([]models.GoodItem, error) {
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		return []models.GoodItem{}, nil
	}

	data, err := ioutil.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var itemData map[string]map[string]interface{}
	if err := json.Unmarshal(data, &itemData); err != nil {
		return nil, err
	}

	var goodItems []models.GoodItem
	for className, itemInfo := range itemData {
		item := models.GoodItem{
			ClassName: className,
			ShortName: parseString(itemInfo["shortName"]),
			ShowItem:  parseBool(itemInfo["showItem"]),
			Group:     parseInt(itemInfo["group"]),
		}
		goodItems = append(goodItems, item)
	}

	return goodItems, nil
}

// LoadMQTTBroker loads MQTT broker configuration
func LoadMQTTBroker(filename string) string {
	if _, err := os.Stat(filename); os.IsNotExist(err) {
		return "219.129.239.39" // Default broker
	}

	data, err := ioutil.ReadFile(filename)
	if err != nil {
		return "219.129.239.39"
	}

	return strings.TrimSpace(string(data))
}

// Helper functions for parsing different types
func parseUint64(value interface{}) uint64 {
	switch v := value.(type) {
	case string:
		if strings.HasPrefix(v, "0x") || strings.HasPrefix(v, "0X") {
			if result, err := strconv.ParseUint(v[2:], 16, 64); err == nil {
				return result
			}
		}
		if result, err := strconv.ParseUint(v, 10, 64); err == nil {
			return result
		}
	case float64:
		return uint64(v)
	case int:
		return uint64(v)
	case int64:
		return uint64(v)
	case uint64:
		return v
	}
	return 0
}

func parseUint32(value interface{}) uint32 {
	switch v := value.(type) {
	case string:
		if strings.HasPrefix(v, "0x") || strings.HasPrefix(v, "0X") {
			if result, err := strconv.ParseUint(v[2:], 16, 32); err == nil {
				return uint32(result)
			}
		}
		if result, err := strconv.ParseUint(v, 10, 32); err == nil {
			return uint32(result)
		}
	case float64:
		return uint32(v)
	case int:
		return uint32(v)
	case uint32:
		return v
	}
	return 0
}

func parseInt64(value interface{}) int64 {
	switch v := value.(type) {
	case string:
		if strings.HasPrefix(v, "0x") || strings.HasPrefix(v, "0X") {
			if result, err := strconv.ParseInt(v[2:], 16, 64); err == nil {
				return result
			}
		}
		if result, err := strconv.ParseInt(v, 10, 64); err == nil {
			return result
		}
	case float64:
		return int64(v)
	case int:
		return int64(v)
	case int64:
		return v
	}
	return 0
}

func parseInt(value interface{}) int {
	switch v := value.(type) {
	case string:
		if strings.HasPrefix(v, "0x") || strings.HasPrefix(v, "0X") {
			if result, err := strconv.ParseInt(v[2:], 16, 32); err == nil {
				return int(result)
			}
		}
		if result, err := strconv.ParseInt(v, 10, 32); err == nil {
			return int(result)
		}
	case float64:
		return int(v)
	case int:
		return v
	}
	return 0
}

func parseBool(value interface{}) bool {
	switch v := value.(type) {
	case bool:
		return v
	case string:
		return v == "true" || v == "1"
	case float64:
		return v != 0
	case int:
		return v != 0
	}
	return false
}

func parseString(value interface{}) string {
	switch v := value.(type) {
	case string:
		return v
	default:
		return ""
	}
}
